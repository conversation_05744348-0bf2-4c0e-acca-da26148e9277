# E-Commerce Authentication System

A full-stack authentication system built with React frontend and Node.js backend, featuring user registration, login, and JWT-based authentication.

## 🚀 Features

- **User Registration**: Secure user signup with validation
- **User Login**: JWT-based authentication system
- **Password Security**: Bcrypt password hashing
- **Input Validation**: Server-side validation with express-validator
- **Database**: SQLite database for user storage
- **CORS Support**: Cross-origin resource sharing enabled
- **Environment Configuration**: Secure environment variable management

## 🛠️ Tech Stack

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **SQLite3** - Database
- **JWT** - Authentication tokens
- **Bcryptjs** - Password hashing
- **Express-validator** - Input validation
- **CORS** - Cross-origin requests
- **Dotenv** - Environment variables

### Frontend
- **React** - UI framework
- **Vite** - Build tool and dev server

## 📁 Project Structure

```
ECOMMERCE-CORES3/
├── client/                 # React frontend
│   ├── src/
│   ├── package.json
│   └── index.html
├── server/                 # Node.js backend
│   ├── index.js           # Main server file
│   ├── package.json
│   └── .env               # Environment variables
├── package.json           # Root package.json
└── README.md
```

## 🔧 Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### 1. Clone the repository
```bash
git clone https://github.com/4LGHA/ECOMMERCE-CORES3.git
cd ECOMMERCE-CORES3
```

### 2. Install dependencies
```bash
# Install root dependencies
npm install

# Install server dependencies
cd server
npm install

# Install client dependencies (when ready)
cd ../client
npm install
```

### 3. Environment Setup
Create a `.env` file in the server directory:
```env
PORT=3001
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
NODE_ENV=development
```

### 4. Start the application
```bash
# Start both frontend and backend (from root directory)
npm run dev

# Or start individually:
# Backend only
cd server && npm run dev

# Frontend only (when ready)
cd client && npm run dev
```

## 📡 API Endpoints

### Authentication Routes

#### Register User
- **POST** `/api/register`
- **Body**:
  ```json
  {
    "username": "string (min 3 chars)",
    "email": "valid email",
    "password": "string (min 6 chars)"
  }
  ```
- **Response**: User object + JWT token

#### Login User
- **POST** `/api/login`
- **Body**:
  ```json
  {
    "email": "valid email",
    "password": "string"
  }
  ```
- **Response**: User object + JWT token

#### Get Profile (Protected)
- **GET** `/api/profile`
- **Headers**: `Authorization: Bearer <token>`
- **Response**: User profile information

## 🗄️ Database Schema

### Users Table
```sql
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE NOT NULL,
  email TEXT UNIQUE NOT NULL,
  password TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 🔒 Security Features

- **Password Hashing**: Bcrypt with salt rounds
- **JWT Tokens**: Secure authentication tokens
- **Input Validation**: Server-side validation
- **CORS Protection**: Configured cross-origin policies
- **Environment Variables**: Sensitive data protection

## 🚧 Current Status

✅ **Completed:**
- Backend API setup with authentication endpoints
- Database setup with SQLite
- User registration and login functionality
- JWT token generation and validation
- Password hashing and security
- Input validation and error handling

🔄 **In Progress:**
- Frontend React components for login/signup forms
- Authentication integration between frontend and backend
- Protected route functionality

📋 **Next Steps:**
- Complete React frontend components
- Implement authentication state management
- Add form validation on frontend
- Create protected routes
- Add user dashboard
- Implement logout functionality
- Add password reset feature

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Open a Pull Request

## 📄 License

This project is for educational/project purposes.
