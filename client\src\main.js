// Configuration - Update this URL when you deploy your backend
const API_BASE_URL = 'https://your-backend-url.railway.app/api'; // Replace with your actual backend URL

// DOM Elements
const showLoginBtn = document.getElementById('showLogin');
const showRegisterBtn = document.getElementById('showRegister');
const loginForm = document.getElementById('loginForm');
const registerForm = document.getElementById('registerForm');
const dashboard = document.getElementById('dashboard');
const messageDiv = document.getElementById('message');
const loginFormElement = document.getElementById('loginFormElement');
const registerFormElement = document.getElementById('registerFormElement');
const logoutBtn = document.getElementById('logoutBtn');
const userInfo = document.getElementById('userInfo');

// State
let currentUser = null;
let authToken = localStorage.getItem('authToken');

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
  if (authToken) {
    checkAuthStatus();
  }
  setupEventListeners();
});

// Event Listeners
function setupEventListeners() {
  showLoginBtn.addEventListener('click', () => showForm('login'));
  showRegisterBtn.addEventListener('click', () => showForm('register'));
  loginFormElement.addEventListener('submit', handleLogin);
  registerFormElement.addEventListener('submit', handleRegister);
  logoutBtn.addEventListener('click', handleLogout);
}

// UI Functions
function showForm(formType) {
  if (formType === 'login') {
    loginForm.classList.remove('hidden');
    registerForm.classList.add('hidden');
    dashboard.classList.add('hidden');
    showLoginBtn.classList.add('active');
    showRegisterBtn.classList.remove('active');
  } else {
    registerForm.classList.remove('hidden');
    loginForm.classList.add('hidden');
    dashboard.classList.add('hidden');
    showRegisterBtn.classList.add('active');
    showLoginBtn.classList.remove('active');
  }
  clearMessage();
}

function showDashboard() {
  loginForm.classList.add('hidden');
  registerForm.classList.add('hidden');
  dashboard.classList.remove('hidden');
  showLoginBtn.classList.remove('active');
  showRegisterBtn.classList.remove('active');
}

function showMessage(message, type = 'error') {
  messageDiv.textContent = message;
  messageDiv.className = `message ${type}`;
}

function clearMessage() {
  messageDiv.textContent = '';
  messageDiv.className = 'message';
}

// API Functions
async function apiCall(endpoint, options = {}) {
  try {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    if (authToken && !config.headers.Authorization) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
}

// Auth Functions
async function handleLogin(e) {
  e.preventDefault();

  const email = document.getElementById('loginEmail').value;
  const password = document.getElementById('loginPassword').value;

  try {
    showMessage('Logging in...', 'info');

    const data = await apiCall('/login', {
      method: 'POST',
      body: JSON.stringify({ email, password })
    });

    authToken = data.token;
    currentUser = data.user;
    localStorage.setItem('authToken', authToken);

    showMessage('Login successful!', 'success');
    displayUserInfo();
    showDashboard();

  } catch (error) {
    showMessage(error.message, 'error');
  }
}

async function handleRegister(e) {
  e.preventDefault();

  const username = document.getElementById('registerUsername').value;
  const email = document.getElementById('registerEmail').value;
  const password = document.getElementById('registerPassword').value;

  try {
    showMessage('Creating account...', 'info');

    const data = await apiCall('/register', {
      method: 'POST',
      body: JSON.stringify({ username, email, password })
    });

    authToken = data.token;
    currentUser = data.user;
    localStorage.setItem('authToken', authToken);

    showMessage('Account created successfully!', 'success');
    displayUserInfo();
    showDashboard();

  } catch (error) {
    showMessage(error.message, 'error');
  }
}

async function checkAuthStatus() {
  try {
    const data = await apiCall('/profile');
    currentUser = data.user;
    displayUserInfo();
    showDashboard();
  } catch (error) {
    // Token is invalid, remove it
    localStorage.removeItem('authToken');
    authToken = null;
    showForm('login');
  }
}

function handleLogout() {
  localStorage.removeItem('authToken');
  authToken = null;
  currentUser = null;
  showForm('login');
  showMessage('Logged out successfully', 'success');
}

function displayUserInfo() {
  if (currentUser) {
    userInfo.innerHTML = `
      <p><strong>ID:</strong> ${currentUser.id}</p>
      <p><strong>Username:</strong> ${currentUser.username}</p>
      <p><strong>Email:</strong> ${currentUser.email}</p>
    `;
  }
}
