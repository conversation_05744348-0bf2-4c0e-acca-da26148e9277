{"name": "ecommerce-auth-system", "version": "1.0.0", "description": "E-commerce authentication system with login and signup", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "build": "cd client && npm run build", "install-all": "npm install && cd server && npm install && cd ../client && npm install"}, "keywords": ["ecommerce", "authentication", "react", "nodejs"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}