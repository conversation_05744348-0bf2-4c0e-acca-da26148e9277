const { Pool } = require('pg');
require('dotenv').config();

// Create PostgreSQL connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// Test database connection
const testConnection = async () => {
  try {
    const client = await pool.connect();
    console.log('✅ Connected to Neon PostgreSQL database');
    client.release();
  } catch (err) {
    console.error('❌ Database connection error:', err.message);
    process.exit(1);
  }
};

// Initialize database tables
const initializeDatabase = async () => {
  try {
    const client = await pool.connect();
    
    // Create users table if it doesn't exist
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(255) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('✅ Database tables initialized');
    client.release();
  } catch (err) {
    console.error('❌ Database initialization error:', err.message);
    throw err;
  }
};

// Database query functions
const db = {
  // Get user by email
  getUserByEmail: async (email) => {
    const client = await pool.connect();
    try {
      const result = await client.query('SELECT * FROM users WHERE email = $1', [email]);
      return result.rows[0];
    } finally {
      client.release();
    }
  },

  // Get user by username
  getUserByUsername: async (username) => {
    const client = await pool.connect();
    try {
      const result = await client.query('SELECT * FROM users WHERE username = $1', [username]);
      return result.rows[0];
    } finally {
      client.release();
    }
  },

  // Get user by ID
  getUserById: async (id) => {
    const client = await pool.connect();
    try {
      const result = await client.query('SELECT id, username, email, created_at FROM users WHERE id = $1', [id]);
      return result.rows[0];
    } finally {
      client.release();
    }
  },

  // Create new user
  createUser: async (username, email, hashedPassword) => {
    const client = await pool.connect();
    try {
      const result = await client.query(
        'INSERT INTO users (username, email, password) VALUES ($1, $2, $3) RETURNING id, username, email, created_at',
        [username, email, hashedPassword]
      );
      return result.rows[0];
    } finally {
      client.release();
    }
  },

  // Check if user exists by email or username
  userExists: async (email, username) => {
    const client = await pool.connect();
    try {
      const result = await client.query(
        'SELECT id FROM users WHERE email = $1 OR username = $2',
        [email, username]
      );
      return result.rows.length > 0;
    } finally {
      client.release();
    }
  }
};

module.exports = {
  pool,
  testConnection,
  initializeDatabase,
  db
};
