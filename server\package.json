{"name": "ecommerce-auth-server", "version": "1.0.0", "description": "Backend server for e-commerce authentication", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "dotenv": "^16.3.1", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}